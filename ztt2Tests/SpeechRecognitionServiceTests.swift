//
//  SpeechRecognitionServiceTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/8/3.
//  语音识别服务测试用例
//

import XCTest
import Speech
import AVFoundation
@testable import ztt2

/**
 * 语音识别服务测试类
 * 测试语音识别功能的各个方面，包括权限管理、录音控制、错误处理等
 */
@MainActor
final class SpeechRecognitionServiceTests: XCTestCase {
    
    var speechService: SpeechRecognitionService!
    
    override func setUp() {
        super.setUp()
        speechService = SpeechRecognitionService()
    }
    
    override func tearDown() {
        speechService = nil
        super.tearDown()
    }
    
    // MARK: - 初始化测试
    
    /**
     * 测试语音识别服务初始化
     */
    func testSpeechRecognitionServiceInitialization() {
        XCTAssertNotNil(speechService, "语音识别服务应该成功初始化")
        XCTAssertFalse(speechService.isRecording, "初始状态应该不在录音")
        XCTAssertEqual(speechService.recognizedText, "", "初始识别文本应该为空")
        XCTAssertNil(speechService.errorMessage, "初始错误信息应该为空")
    }
    
    // MARK: - 权限管理测试
    
    /**
     * 测试权限检查功能
     */
    func testCheckPermissions() {
        speechService.checkPermissions()
        
        // 验证权限状态被正确设置
        XCTAssertNotEqual(speechService.authorizationStatus, .notDetermined, "语音识别权限状态应该被检查")
        XCTAssertNotEqual(speechService.microphonePermissionStatus, .undetermined, "麦克风权限状态应该被检查")
    }
    
    /**
     * 测试权限请求功能
     */
    func testRequestPermissions() async {
        await speechService.requestPermissions()
        
        // 验证权限请求后状态更新
        XCTAssertTrue(
            speechService.authorizationStatus == .authorized || 
            speechService.authorizationStatus == .denied ||
            speechService.authorizationStatus == .restricted,
            "语音识别权限应该有明确的状态"
        )
        
        XCTAssertTrue(
            speechService.microphonePermissionStatus == .granted ||
            speechService.microphonePermissionStatus == .denied,
            "麦克风权限应该有明确的状态"
        )
    }
    
    // MARK: - 录音控制测试
    
    /**
     * 测试录音状态管理
     */
    func testRecordingStateManagement() {
        // 初始状态
        XCTAssertFalse(speechService.isRecording, "初始状态应该不在录音")
        
        // 停止录音（即使没有开始）
        speechService.stopRecording()
        XCTAssertFalse(speechService.isRecording, "停止录音后状态应该为false")
    }
    
    /**
     * 测试录音权限检查
     */
    func testCanStartRecording() {
        let canRecord = speechService.canStartRecording
        
        // 验证权限检查逻辑
        if speechService.authorizationStatus == .authorized &&
           speechService.microphonePermissionStatus == .granted &&
           !speechService.isRecording {
            XCTAssertTrue(canRecord, "满足所有条件时应该可以开始录音")
        } else {
            XCTAssertFalse(canRecord, "不满足条件时不应该可以开始录音")
        }
    }
    
    // MARK: - 错误处理测试
    
    /**
     * 测试错误信息清除
     */
    func testClearError() {
        // 模拟设置错误信息
        speechService.errorMessage = "测试错误"
        XCTAssertNotNil(speechService.errorMessage, "错误信息应该被设置")
        
        // 清除错误信息
        speechService.clearError()
        XCTAssertNil(speechService.errorMessage, "错误信息应该被清除")
    }
    
    /**
     * 测试权限状态消息
     */
    func testPermissionStatusMessage() {
        let message = speechService.permissionStatusMessage
        
        if speechService.authorizationStatus != .authorized {
            XCTAssertEqual(message, "需要语音识别权限", "应该提示需要语音识别权限")
        } else if speechService.microphonePermissionStatus != .granted {
            XCTAssertEqual(message, "需要麦克风权限", "应该提示需要麦克风权限")
        } else {
            XCTAssertEqual(message, "", "所有权限都有时消息应该为空")
        }
    }
    
    // MARK: - 语音识别错误测试
    
    /**
     * 测试语音识别错误类型
     */
    func testSpeechRecognitionErrors() {
        let errors: [SpeechRecognitionError] = [
            .speechRecognitionNotAuthorized,
            .microphoneNotAuthorized,
            .speechRecognizerNotAvailable,
            .unableToCreateRequest,
            .recognitionFailed("测试错误")
        ]
        
        for error in errors {
            XCTAssertNotNil(error.errorDescription, "每个错误类型都应该有描述信息")
        }
        
        // 测试特定错误描述
        XCTAssertEqual(
            SpeechRecognitionError.speechRecognitionNotAuthorized.errorDescription,
            "语音识别权限未授权"
        )
        
        XCTAssertEqual(
            SpeechRecognitionError.microphoneNotAuthorized.errorDescription,
            "麦克风权限未授权"
        )
        
        XCTAssertEqual(
            SpeechRecognitionError.recognitionFailed("测试").errorDescription,
            "语音识别失败: 测试"
        )
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试语音识别服务初始化性能
     */
    func testSpeechRecognitionServiceInitializationPerformance() {
        measure {
            let service = SpeechRecognitionService()
            XCTAssertNotNil(service)
        }
    }
    
    /**
     * 测试权限检查性能
     */
    func testCheckPermissionsPerformance() {
        measure {
            speechService.checkPermissions()
        }
    }
    
    // MARK: - 集成测试
    
    /**
     * 测试语音识别服务与UI的集成
     */
    func testSpeechRecognitionServiceUIIntegration() {
        // 验证Published属性可以被观察
        let expectation = XCTestExpectation(description: "语音识别状态变化")
        
        let cancellable = speechService.$isRecording.sink { isRecording in
            if !isRecording {
                expectation.fulfill()
            }
        }
        
        // 触发状态变化
        speechService.stopRecording()
        
        wait(for: [expectation], timeout: 1.0)
        cancellable.cancel()
    }
    
    /**
     * 测试语音识别文本更新
     */
    func testRecognizedTextUpdate() {
        let expectation = XCTestExpectation(description: "识别文本更新")

        let cancellable = speechService.$recognizedText.sink { text in
            if !text.isEmpty {
                expectation.fulfill()
            }
        }

        // 模拟文本更新
        speechService.recognizedText = "测试文本"

        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(speechService.recognizedText, "测试文本")
        cancellable.cancel()
    }

    /**
     * 测试累积文本清空功能
     */
    func testClearAccumulatedText() {
        // 设置一些识别文本
        speechService.recognizedText = "第一句话"
        XCTAssertEqual(speechService.recognizedText, "第一句话")

        // 清空累积文本
        speechService.clearAccumulatedText()

        // 验证文本被清空
        XCTAssertEqual(speechService.recognizedText, "")
    }
}
